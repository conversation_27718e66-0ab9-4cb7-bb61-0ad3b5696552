/**
 * Repository Pattern Types for PCF Control
 * Defines interfaces and types for data access layer
 */

import type { AttachedFile, BpfActionName, BpfStageInfo } from './BpfTypes';

// Base repository result type
export interface RepositoryResult<T = unknown> {
    success: boolean;
    data?: T;
    error?: string;
}

// Repository operation options
export interface RepositoryOptions {
    timeout?: number;
    retryCount?: number;
    enableLogging?: boolean;
}

// BPF Repository Interface
export interface IBpfRepository {
    // Process operations
    getCurrentProcess(): Promise<RepositoryResult<unknown>>;
    getProcessStages(): Promise<RepositoryResult<BpfStageInfo[]>>;
    getCurrentStage(): Promise<RepositoryResult<BpfStageInfo>>;
    
    // Stage navigation
    moveToNextStage(): Promise<RepositoryResult<boolean>>;
    moveToPreviousStage(): Promise<RepositoryResult<boolean>>;
    moveToStage(stageId: string): Promise<RepositoryResult<boolean>>;
    
    // Stage information
    canMoveToNext(): Promise<RepositoryResult<boolean>>;
    canMoveToPrevious(): Promise<RepositoryResult<boolean>>;
    isFirstStage(): Promise<RepositoryResult<boolean>>;
    isLastStage(): Promise<RepositoryResult<boolean>>;
    
    // Entity context
    getEntityInfo(): Promise<RepositoryResult<EntityInfo>>;
    getEntityId(): Promise<RepositoryResult<string>>;
    getEntityLogicalName(): Promise<RepositoryResult<string>>;
}

// Timeline Repository Interface
export interface ITimelineRepository {
    // Note operations
    createNote(entityId: string, subject: string, noteText: string, attachedFiles?: AttachedFile[]): Promise<RepositoryResult<string>>;
    getNote(noteId: string): Promise<RepositoryResult<TimelineNote>>;
    getNotesBySubject(entityId: string, subjectPattern: string): Promise<RepositoryResult<TimelineNote[]>>;
    updateNote(noteId: string, noteText: string): Promise<RepositoryResult<boolean>>;
    deleteNote(noteId: string): Promise<RepositoryResult<boolean>>;
    
    // Stage-specific operations
    createStageNote(stageId: string, comment: string, action: BpfActionName, attachedFiles?: AttachedFile[]): Promise<RepositoryResult<string>>;
    getLatestStageComment(stageId: string): Promise<RepositoryResult<string>>;
    getStageNotes(stageId: string): Promise<RepositoryResult<TimelineNote[]>>;
    
    // Configuration
    getEntityInfo(): Promise<RepositoryResult<EntityInfo>>;
    setEntityInfo(entityInfo: EntityInfo): Promise<RepositoryResult<boolean>>;
}

// Unified Entity information type (replaces both EntityInfo and TimelineConfig)
export interface EntityInfo {
    entityId: string;
    entityLogicalName: string;
    entitySetName: string;
    objectTypeCode: number;
    entityDisplayName?: string;
    subjectPrefix?: string;
}

// Timeline note type
export interface TimelineNote {
    noteId: string;
    subject: string;
    noteText: string;
    createdOn: Date;
    createdBy?: string;
    modifiedOn?: Date;
    modifiedBy?: string;
    attachments?: AttachedFile[];
}

// TimelineConfig has been consolidated into EntityInfo

// Dynamics 365 API interfaces
export interface DynamicsNote {
    annotationid: string;
    subject: string;
    notetext: string;
    createdon: string;
    filename?: string;
    filesize?: number;
    mimetype?: string;
    documentbody?: string;
}

export interface DynamicsQueryResult {
    entities: DynamicsNote[];
    value?: DynamicsNote[];
}

// Repository factory functions are now simple functions, no interface needed

// Repository error types
export enum RepositoryErrorType {
    NETWORK_ERROR = 'NETWORK_ERROR',
    PERMISSION_ERROR = 'PERMISSION_ERROR',
    VALIDATION_ERROR = 'VALIDATION_ERROR',
    NOT_FOUND = 'NOT_FOUND',
    TIMEOUT = 'TIMEOUT',
    UNKNOWN = 'UNKNOWN'
}

export interface RepositoryError {
    type: RepositoryErrorType;
    message: string;
    details?: unknown;
    timestamp: Date;
}

// Repository base class interface
export interface IBaseRepository {
    readonly options: RepositoryOptions;
    handleError(error: unknown): RepositoryError;
    logOperation(operation: string, data?: unknown): void;
}
