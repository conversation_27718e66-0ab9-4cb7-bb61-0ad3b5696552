/*
*This is auto generated from the ControlManifest.Input.xml file
*/

// Define IInputs and IOutputs Type. They should match with ControlManifest.
export interface IInputs {
    sourceControl: ComponentFramework.PropertyTypes.StringProperty;
    popupTitle: ComponentFramework.PropertyTypes.StringProperty;
    textLabel: ComponentFramework.PropertyTypes.StringProperty;
    textPlaceHolder: ComponentFramework.PropertyTypes.StringProperty;
    language: ComponentFramework.PropertyTypes.StringProperty;
    enableFileAttachment: ComponentFramework.PropertyTypes.TwoOptionsProperty;
    allowedFileTypes: ComponentFramework.PropertyTypes.StringProperty;
    maxFileSize: ComponentFramework.PropertyTypes.WholeNumberProperty;
}
export interface IOutputs {
    sourceControl?: string;
}
