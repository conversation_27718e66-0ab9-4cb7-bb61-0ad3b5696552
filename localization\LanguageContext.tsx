/**
 * Simplified Language Context for Process Checkpoint PCF Control
 * Lightweight language management following Repository Pattern principles
 */

import * as React from 'react';
import { getLocalizedStrings, type SupportedLanguage, type LocalizationStrings } from '../hooks/UseLocalization';

interface LanguageContextType {
    strings: LocalizationStrings;
}

// Simple context for strings only - language switching handled at PCF level
const LanguageContext = React.createContext<LanguageContextType>({
    strings: getLocalizedStrings('vi')
});

interface LanguageProviderProps {
    children: React.ReactNode;
    language?: SupportedLanguage;
}

/**
 * Simplified Language Provider
 * Provides localized strings based on PCF property, no dynamic switching
 */
export const LanguageProvider: React.FC<LanguageProviderProps> = ({
    children,
    language = 'vi'
}) => {
    // Static strings based on PCF property - no state needed
    const strings = React.useMemo(() => {
        return getLocalizedStrings(language);
    }, [language]);

    const contextValue = React.useMemo(() => ({
        strings
    }), [strings]);

    return (
        <LanguageContext.Provider value={contextValue}>
            {children}
        </LanguageContext.Provider>
    );
};

/**
 * Primary hook to get localized strings
 * Simplified for PCF Control usage pattern
 */
export const useStrings = (): LocalizationStrings => {
    const context = React.useContext(LanguageContext);

    // Graceful fallback for PCF environment
    if (!context) {
        return getLocalizedStrings('vi');
    }

    return context.strings;
};
