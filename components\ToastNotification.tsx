import * as React from "react";

import {
    To<PERSON>,
    ToastBody,
    Toaster,
    ToastTitle,
    useToastController,
    type ToastIntent as FluentToastIntent
} from "@fluentui/react-components";

import type { ToastIntent } from "../types";
import { ToastIntentType } from "../types";

export interface ToastMessage {
    id: string;
    intent: FluentToastIntent;
    title: string;
    message?: string;
}

interface ToastNotificationProps {
    messages: ToastMessage[];
    onDismiss: (id: string) => void;
}

export const ToastNotification: React.FC<ToastNotificationProps> = ({
    messages,
    onDismiss
}) => {
    const { dispatchToast } = useToastController();

    React.useEffect(() => {
        messages.forEach((msg) => {
            dispatchToast(
                <Toast>
                    <ToastTitle>{msg.title}</ToastTitle>
                    {msg.message && <ToastBody>{msg.message}</ToastBody>}
                </Toast>,
                {
                    intent: msg.intent,
                    timeout: msg.intent === (ToastIntentType.ERROR as FluentToastIntent) ? 5000 : 3000,
                    onStatusChange: (_, data) => {
                        if (data.status === 'dismissed') {
                            onDismiss(msg.id);
                        }
                    }
                }
            );
        });
    }, [messages, dispatchToast, onDismiss]);

    return <Toaster />;
};

// Hook for managing toast messages
export const useToastMessages = () => {
    const [messages, setMessages] = React.useState<ToastMessage[]>([]);

    const showToast = React.useCallback((
        intent: ToastIntent,
        title: string,
        message?: string
    ) => {
        const id = `toast-${Date.now()}-${Math.random()}`;
        const newMessage: ToastMessage = { id, intent: intent as FluentToastIntent, title, message };
        setMessages(prev => [...prev, newMessage]);
    }, []);

    const dismissToast = React.useCallback((id: string) => {
        setMessages(prev => prev.filter(msg => msg.id !== id));
    }, []);

    const showSuccess = React.useCallback((title: string, message?: string) => {
        showToast(ToastIntentType.SUCCESS, title, message);
    }, [showToast]);

    const showError = React.useCallback((title: string, message?: string) => {
        showToast(ToastIntentType.ERROR, title, message);
    }, [showToast]);

    const showWarning = React.useCallback((title: string, message?: string) => {
        showToast(ToastIntentType.WARNING, title, message);
    }, [showToast]);

    const showInfo = React.useCallback((title: string, message?: string) => {
        showToast(ToastIntentType.INFO, title, message);
    }, [showToast]);

    return {
        messages,
        dismissToast,
        showSuccess,
        showError,
        showWarning,
        showInfo
    };
};
