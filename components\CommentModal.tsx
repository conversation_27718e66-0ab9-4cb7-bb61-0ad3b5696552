import * as React from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>
} from "@fluentui/react-components";
import { FileAttachment } from './FileAttachment';
import { useStrings } from '../localization/LanguageContext';
import { BpfActionType, DEFAULT_FILE_CONFIG } from "../types";
import type { BpfActionResult, CommentModalProps, FileAttachmentConfig } from "../types";

const containerStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    zIndex: 999999,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '20px'
};

const inputContainerStyle: React.CSSProperties = {
    backgroundColor: 'white',
    borderRadius: '8px',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4)',
    width: '100%',
    maxWidth: '800px',
    maxHeight: '90vh',
    display: 'flex',
    flexDirection: 'column'
};

const headerStyle: React.CSSProperties = {
    padding: '20px 24px 16px',
    borderBottom: '1px solid #e1e1e1'
};

const titleStyle: React.CSSProperties = {
    margin: 0,
    fontSize: '18px',
    fontWeight: '600'
};

const contentStyle: React.CSSProperties = {
    padding: '20px 24px',
    flex: 1,
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column'
};

const fieldStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    flex: 1
};

const textareaStyle: React.CSSProperties = {
    width: '100%',
    maxHeight: '60vh',
    flex: 1
};

const footerStyle: React.CSSProperties = {
    padding: '16px 24px',
    borderTop: '1px solid #e1e1e1',
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '8px'
};

export const CommentModal: React.FC<CommentModalProps> = ({
    open,
    onOpenChange,
    value = "",
    onChange,
    modalTitle,
    textLabel,
    textPlaceHolder,
    bpfActionHandlers,
    buttonConfig,
    isProcessing = false,
    readOnly = false,
    enableFileAttachment = false,
    allowedFileTypes = [],
    maxFileSize = 10,
    attachedFiles = [],
    onFilesChange
}) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null);
    const [processing, setProcessing] = React.useState(false);
    const [loadingAction, setLoadingAction] = React.useState<string | null>(null);

    const strings = useStrings();
    const fileConfig: FileAttachmentConfig = React.useMemo(() => ({
        enabled: enableFileAttachment,
        allowedTypes: allowedFileTypes.length > 0 ? allowedFileTypes : DEFAULT_FILE_CONFIG.allowedTypes,
        maxSizeInMB: maxFileSize > 0 ? maxFileSize : DEFAULT_FILE_CONFIG.maxSizeInMB,
        maxFiles: DEFAULT_FILE_CONFIG.maxFiles
    }), [enableFileAttachment, allowedFileTypes, maxFileSize]);

    React.useEffect(() => {
        if (open && textareaRef.current) {
            textareaRef.current.focus();
        }
    }, [open]);

    const handleTextareaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        if (onChange) {
            onChange(event.target.value);
        }
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
        if (event.key === 'Escape') {
            onOpenChange(false);
        }
    };

    const handleContentKeyDown = (event: React.KeyboardEvent) => {
        // Stop propagation to prevent closing the dialog when interacting with content
        event.stopPropagation();
    };

    const handleBpfAction = (actionType: BpfActionType) => {
        console.log('🎬 MODAL DEBUG - handleBpfAction called:', {
            actionType,
            hasValue: !!value?.trim(),
            hasFiles: !!attachedFiles?.length,
            readOnly,
            processing,
            isProcessing
        });

        if (processing || isProcessing) return;

        setProcessing(true);
        setLoadingAction(actionType);

        if (actionType === BpfActionType.CANCEL) {
            console.log('🚫 MODAL DEBUG - Cancel action');
            if (bpfActionHandlers?.onCancel) {
                bpfActionHandlers.onCancel();
            } else {
                onOpenChange(false);
            }
            setProcessing(false);
            setLoadingAction(null);
            return;
        }

        let actionPromise: Promise<BpfActionResult> | null = null;

        switch (actionType) {
            case BpfActionType.SUBMIT:
                console.log('📤 MODAL DEBUG - Submit action');
                if (bpfActionHandlers?.onSubmit) {
                    actionPromise = bpfActionHandlers.onSubmit();
                }
                break;

            case BpfActionType.APPROVE:
                console.log('✅ MODAL DEBUG - Approve action');
                if (bpfActionHandlers?.onApprove) {
                    actionPromise = bpfActionHandlers.onApprove();
                }
                break;

            case BpfActionType.REJECT:
                console.log('❌ MODAL DEBUG - Reject action');
                if (bpfActionHandlers?.onReject) {
                    actionPromise = bpfActionHandlers.onReject();
                }
                break;
        }

        if (actionPromise) {
            console.log('🔄 MODAL DEBUG - Executing action promise');
            void actionPromise
                .then((result) => {
                    console.log('✅ MODAL DEBUG - Action completed:', result);
                    if (result.success) {
                        onOpenChange(false);
                    }
                    return result;
                })
                .catch((error) => {
                    console.log('❌ MODAL DEBUG - Action failed:', error);
                    return { success: false };
                })
                .finally(() => {
                    console.log('🏁 MODAL DEBUG - Action finalized');
                    setProcessing(false);
                    setLoadingAction(null);
                });
        } else {
            console.log('❌ MODAL DEBUG - No action promise available');
            setProcessing(false);
            setLoadingAction(null);
        }
    };

    if (!open) {
        return null;
    }

    return (
        <div
            onKeyDown={handleKeyDown}
            style={containerStyle}
            tabIndex={-1}
            role="dialog"
            aria-modal="true"
        >
            <div
                style={inputContainerStyle}
                onClick={(e) => e.stopPropagation()}
                onKeyDown={handleContentKeyDown}
                role="document"
            >
                <div style={headerStyle}>
                    <h2 style={titleStyle}>
                        {modalTitle}
                    </h2>
                </div>

                <div style={contentStyle}>
                    {readOnly && (
                        <div style={{
                            backgroundColor: '#f3f2f1',
                            borderLeft: '3px solid #8a8886',
                            padding: '8px 12px',
                            marginBottom: '12px',
                            fontSize: '13px',
                            color: '#605e5c'
                        }}>
                            {strings.ui.readOnly.description}
                        </div>
                    )}

                    <Field
                        label={
                            <span style={{ color: readOnly ? '#8a8886' : '#323130' }}>
                                {textLabel}
                            </span>
                        }
                        style={fieldStyle}
                    >
                        <Textarea
                            ref={textareaRef}
                            value={value}
                            onChange={handleTextareaChange}
                            rows={15}
                            style={{
                                ...textareaStyle,
                                backgroundColor: readOnly ? '#f8f8f8' : '#ffffff',
                                borderColor: readOnly ? '#d2d0ce' : '#605e5c',
                                cursor: readOnly ? 'not-allowed' : 'text',
                                color: readOnly ? '#8a8886' : '#323130'
                            }}
                            placeholder={readOnly
                                ? (value ? '' : strings.ui.placeholders.readOnlyComment)
                                : textPlaceHolder
                            }
                            readOnly={readOnly}
                            aria-label={textLabel}
                            aria-describedby="textarea-description"
                            aria-required={!readOnly}
                        />
                    </Field>

                    {fileConfig.enabled && (
                        <FileAttachment
                            files={attachedFiles}
                            onFilesChange={onFilesChange ?? (() => {
                                // Default empty handler for file changes
                            })}
                            config={fileConfig}
                            readOnly={readOnly}
                        />
                    )}
                </div>

                <div style={footerStyle}>
                    {buttonConfig?.showCancel !== false && (
                        <Button
                            appearance="secondary"
                            onClick={() => void handleBpfAction(BpfActionType.CANCEL)}
                            disabled={processing || isProcessing}
                        >
                            {strings.ui.buttons.cancel}
                        </Button>
                    )}

                    {buttonConfig?.showReject && (
                        <Button
                            appearance="secondary"
                            onClick={() => void handleBpfAction(BpfActionType.REJECT)}
                            disabled={processing || isProcessing}
                            style={{
                                backgroundColor: '#d13438',
                                color: 'white',
                                border: 'none'
                            }}
                        >
                            {(processing || isProcessing) && loadingAction === BpfActionType.REJECT ?
                                <><Spinner size="tiny" /> {strings.messages.loading.rejecting}</> : strings.ui.buttons.reject}
                        </Button>
                    )}

                    {buttonConfig?.showSubmit && (
                        <Button
                            appearance="primary"
                            onClick={() => void handleBpfAction(BpfActionType.SUBMIT)}
                            disabled={processing || isProcessing}
                        >
                            {(processing || isProcessing) && loadingAction === BpfActionType.SUBMIT ?
                                <><Spinner size="tiny" /> {strings.messages.loading.submitting}</> : strings.ui.buttons.submit}
                        </Button>
                    )}

                    {buttonConfig?.showApprove && (
                        <Button
                            appearance="primary"
                            onClick={() => void handleBpfAction(BpfActionType.APPROVE)}
                            disabled={processing || isProcessing}
                            style={{
                                backgroundColor: '#107c10',
                                color: 'white',
                                border: 'none'
                            }}
                        >
                            {(processing || isProcessing) && loadingAction === BpfActionType.APPROVE ?
                                <><Spinner size="tiny" /> {strings.messages.loading.approving}</> : strings.ui.buttons.approve}
                        </Button>
                    )}
                </div>
            </div>
        </div>
    );
};