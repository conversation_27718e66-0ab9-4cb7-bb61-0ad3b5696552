/**
 * Repository Pattern Types for PCF Control
 * Defines interfaces and types for data access layer
 */

import type { AttachedFile, BpfActionName, BpfStageInfo } from './BpfTypes';

export interface RepositoryResult<T = unknown> {
    success: boolean;
    data?: T;
    error?: string;
}

export interface RepositoryOptions {
    timeout?: number;
    retryCount?: number;
}

export interface IBpfRepository {
    getCurrentProcess(): Promise<RepositoryResult<unknown>>;
    getProcessStages(): Promise<RepositoryResult<BpfStageInfo[]>>;
    getCurrentStage(): Promise<RepositoryResult<BpfStageInfo>>;

    moveToNextStage(): Promise<RepositoryResult<boolean>>;
    moveToPreviousStage(): Promise<RepositoryResult<boolean>>;
    moveToStage(stageId: string): Promise<RepositoryResult<boolean>>;

    canMoveToNext(): Promise<RepositoryResult<boolean>>;
    canMoveToPrevious(): Promise<RepositoryResult<boolean>>;
    isFirstStage(): Promise<RepositoryResult<boolean>>;
    isLastStage(): Promise<RepositoryResult<boolean>>;

    getEntityInfo(): Promise<RepositoryResult<EntityInfo>>;
    getEntityId(): Promise<RepositoryResult<string>>;
    getEntityLogicalName(): Promise<RepositoryResult<string>>;
}

export interface ITimelineRepository {
    createNote(entityId: string, subject: string, noteText: string, attachedFiles?: AttachedFile[]): Promise<RepositoryResult<string>>;
    getNote(noteId: string): Promise<RepositoryResult<TimelineNote>>;
    getNotesBySubject(entityId: string, subjectPattern: string): Promise<RepositoryResult<TimelineNote[]>>;
    updateNote(noteId: string, noteText: string): Promise<RepositoryResult<boolean>>;
    deleteNote(noteId: string): Promise<RepositoryResult<boolean>>;

    createStageNote(stageId: string, comment: string, action: BpfActionName, attachedFiles?: AttachedFile[]): Promise<RepositoryResult<string>>;
    getLatestStageComment(stageId: string): Promise<RepositoryResult<string>>;
    getStageNotes(stageId: string): Promise<RepositoryResult<TimelineNote[]>>;

    getEntityInfo(): Promise<RepositoryResult<EntityInfo>>;
    setEntityInfo(entityInfo: EntityInfo): Promise<RepositoryResult<boolean>>;
}

export interface EntityInfo {
    entityId: string;
    entityLogicalName: string;
    entitySetName: string;
    objectTypeCode: number;
    entityDisplayName?: string;
}

export interface TimelineConfig {
    subjectPrefix: string;
}

export const DEFAULT_TIMELINE_CONFIG: TimelineConfig = {
    subjectPrefix: 'PCP'
};

export interface TimelineNote {
    noteId: string;
    subject: string;
    noteText: string;
    createdOn: Date;
    createdBy?: string;
    modifiedOn?: Date;
    modifiedBy?: string;
    attachments?: AttachedFile[];
}

export interface DynamicsNote {
    annotationid: string;
    subject: string;
    notetext: string;
    createdon: string;
    filename?: string;
    filesize?: number;
    mimetype?: string;
    documentbody?: string;
}

export interface DynamicsQueryResult {
    entities: DynamicsNote[];
    value?: DynamicsNote[];
}

export enum RepositoryErrorType {
    NETWORK_ERROR = 'NETWORK_ERROR',
    PERMISSION_ERROR = 'PERMISSION_ERROR',
    VALIDATION_ERROR = 'VALIDATION_ERROR',
    NOT_FOUND = 'NOT_FOUND',
    TIMEOUT = 'TIMEOUT',
    UNKNOWN = 'UNKNOWN'
}

export interface RepositoryError {
    type: RepositoryErrorType;
    message: string;
    details?: unknown;
    timestamp: Date;
}

export interface IBaseRepository {
    readonly options: RepositoryOptions;
    handleError(error: unknown): RepositoryError;
}
