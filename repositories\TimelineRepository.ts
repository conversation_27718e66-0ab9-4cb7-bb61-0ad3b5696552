/**
 * Timeline Repository Implementation
 * Handles all Timeline/Notes data access operations for Dynamics 365
 */

import type {
    AttachedFile,
    BpfActionName,
    DynamicsNote,
    DynamicsQueryResult,
    EntityInfo,
    ITimelineRepository,
    RepositoryError,
    RepositoryOptions,
    RepositoryResult,
    TimelineNote
} from '../types';
import { RepositoryErrorType } from '../types';

interface XrmWindow extends Window {
    Xrm?: {
        WebApi?: {
            createRecord: (entityLogicalName: string, data: Record<string, unknown>) => Promise<{ annotationid: string }>;
            retrieveMultipleRecords: (entityLogicalName: string, options?: string) => Promise<DynamicsQueryResult>;
            retrieveRecord: (entityLogicalName: string, id: string, options?: string) => Promise<DynamicsNote>;
            updateRecord: (entityLogicalName: string, id: string, data: Record<string, unknown>) => Promise<void>;
            deleteRecord: (entityLogicalName: string, id: string) => Promise<void>;
        };
    };
}

interface NoteData {
    subject: string;
    notetext: string;
    [key: string]: unknown;
}

export class TimelineRepository implements ITimelineRepository {
    public readonly options: RepositoryOptions;
    private entityInfo: EntityInfo;

    constructor(entityInfo: EntityInfo, options: RepositoryOptions = {}) {
        this.entityInfo = entityInfo;
        this.options = {
            timeout: 5000,
            retryCount: 3,
            ...options
        };
    }

    // Note operations
    public createNote(
        entityId: string,
        subject: string,
        noteText: string,
        attachedFiles?: AttachedFile[]
    ): Promise<RepositoryResult<string>> {
        try {
            const noteData = this.buildNoteData(entityId, subject, noteText, attachedFiles?.[0]);

            return this.executeCreateNote(noteData)
                .then((result) => {
                    if (!result) {
                        return {
                            success: false,
                            error: 'Failed to create note'
                        };
                    }

                    return {
                        success: true,
                        data: result.annotationid
                    };
                })
                .catch((error) => {
                    const repoError = this.handleError(error);
                    return {
                        success: false,
                        error: repoError.message
                    };
                });
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }

    public getNote(noteId: string): Promise<RepositoryResult<TimelineNote>> {
        try {
            const xrmWindow = window as XrmWindow;
            if (!xrmWindow.Xrm?.WebApi) {
                return Promise.resolve({
                    success: false,
                    error: 'Xrm.WebApi not available'
                });
            }

            return xrmWindow.Xrm.WebApi.retrieveRecord(
                'annotation',
                noteId,
                '?$select=annotationid,subject,notetext,createdon,filename,filesize,mimetype'
            )
                .then((note) => {
                    const timelineNote = this.convertToTimelineNote(note);

                    return {
                        success: true,
                        data: timelineNote
                    };
                })
                .catch((error) => {
                    const repoError = this.handleError(error);
                    return {
                        success: false,
                        error: repoError.message
                    };
                });
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }

    public getNotesBySubject(
        entityId: string,
        subjectPattern: string
    ): Promise<RepositoryResult<TimelineNote[]>> {
        try {
            const fetchXml = this.buildSubjectFetchXml(entityId, subjectPattern);

            return this.executeFetchXml(fetchXml)
                .then((result) => {
                    if (!result?.entities || !Array.isArray(result.entities)) {
                        return {
                            success: true,
                            data: []
                        };
                    }

                    const timelineNotes = result.entities.map(note => this.convertToTimelineNote(note));

                    return {
                        success: true,
                        data: timelineNotes
                    };
                })
                .catch((error) => {
                    const repoError = this.handleError(error);
                    return {
                        success: false,
                        error: repoError.message
                    };
                });
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }

    public updateNote(noteId: string, noteText: string): Promise<RepositoryResult<boolean>> {
        try {
            const xrmWindow = window as XrmWindow;
            if (!xrmWindow.Xrm?.WebApi) {
                return Promise.resolve({
                    success: false,
                    error: 'Xrm.WebApi not available'
                });
            }

            return xrmWindow.Xrm.WebApi.updateRecord('annotation', noteId, {
                notetext: noteText
            })
                .then(() => ({
                    success: true,
                    data: true
                }))
                .catch((error) => {
                    const repoError = this.handleError(error);
                    return {
                        success: false,
                        error: repoError.message
                    };
                });
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }

    public deleteNote(noteId: string): Promise<RepositoryResult<boolean>> {
        try {
            const xrmWindow = window as XrmWindow;
            if (!xrmWindow.Xrm?.WebApi) {
                return Promise.resolve({
                    success: false,
                    error: 'Xrm.WebApi not available'
                });
            }

            return xrmWindow.Xrm.WebApi.deleteRecord('annotation', noteId)
                .then(() => ({
                    success: true,
                    data: true
                }))
                .catch((error) => {
                    const repoError = this.handleError(error);
                    return {
                        success: false,
                        error: repoError.message
                    };
                });
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }

    // Stage-specific operations
    public createStageNote(
        stageId: string,
        comment: string,
        action: BpfActionName,
        attachedFiles?: AttachedFile[]
    ): Promise<RepositoryResult<string>> {
        try {
            const subject = this.generateStageSubject(stageId, action, attachedFiles?.[0]);
            const noteText = attachedFiles?.length ?
                comment || `File attachment: ${attachedFiles[0].name}` :
                comment;

            return this.createNote(this.entityInfo.entityId, subject, noteText, attachedFiles);
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }

    public getLatestStageComment(stageId: string): Promise<RepositoryResult<string>> {
        try {
            return this.getStageNotes(stageId)
                .then((stageNotesResult) => {
                    if (!stageNotesResult.success || !stageNotesResult.data || stageNotesResult.data.length === 0) {
                        return {
                            success: true,
                            data: ''
                        };
                    }

                    const latest = stageNotesResult.data.sort((a, b) =>
                        new Date(b.createdOn).getTime() - new Date(a.createdOn).getTime()
                    )[0];

                    return {
                        success: true,
                        data: latest.noteText
                    };
                })
                .catch((error) => {
                    const repoError = this.handleError(error);
                    return {
                        success: false,
                        error: repoError.message
                    };
                });
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }

    public async getStageNotes(stageId: string): Promise<RepositoryResult<TimelineNote[]>> {
        try {
            const shortId = this.getShortStageId(stageId);
            const subjectPattern = `PCP_${shortId}_%`;
            
            return await this.getNotesBySubject(this.entityInfo.entityId, subjectPattern);
        } catch (error) {
            const repoError = this.handleError(error);
            return {
                success: false,
                error: repoError.message
            };
        }
    }

    // Configuration
    public getEntityInfo(): Promise<RepositoryResult<EntityInfo>> {
        try {
            return Promise.resolve({
                success: true,
                data: { ...this.entityInfo }
            });
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }

    public setEntityInfo(entityInfo: EntityInfo): Promise<RepositoryResult<boolean>> {
        try {
            this.entityInfo = { ...entityInfo };

            return Promise.resolve({
                success: true,
                data: true
            });
        } catch (error) {
            const repoError = this.handleError(error);
            return Promise.resolve({
                success: false,
                error: repoError.message
            });
        }
    }

    // Private helper methods
    private buildNoteData(
        entityId: string,
        subject: string,
        noteText: string,
        attachedFile?: AttachedFile
    ): NoteData {
        const noteData: NoteData = {
            subject,
            notetext: noteText,
            [`objectid_${this.entityInfo.entityLogicalName}@odata.bind`]:
                `/${this.entityInfo.entitySetName}(${entityId})`
        };

        // Add file attachment data if provided
        if (attachedFile) {
            noteData.filename = attachedFile.name;
            noteData.documentbody = attachedFile.content;
            noteData.mimetype = attachedFile.type;
            noteData.filesize = attachedFile.size;
        }

        return noteData;
    }

    private generateStageSubject(stageId: string, action: string, attachedFile?: AttachedFile): string {
        const shortId = this.getShortStageId(stageId);
        const baseSubject = `PCP_${shortId}_${action}`;
        
        return attachedFile ? `${baseSubject} - ${attachedFile.name}` : baseSubject;
    }

    private getShortStageId(stageId: string): string {
        return stageId.substring(0, 8);
    }

    private buildSubjectFetchXml(entityId: string, subjectPattern: string): string {
        return `<fetch version="1.0" output-format="xml-platform" mapping="logical" distinct="false">
            <entity name="annotation">
                <attribute name="annotationid" />
                <attribute name="subject" />
                <attribute name="notetext" />
                <attribute name="createdon" />
                <attribute name="filename" />
                <attribute name="filesize" />
                <attribute name="mimetype" />
                <order attribute="createdon" descending="true" />
                <filter type="and">
                    <condition attribute="subject" operator="like" value="${subjectPattern}"/>
                    <condition attribute="objecttypecode" operator="eq" value="${this.entityInfo.objectTypeCode}"/>
                    <condition attribute="objectid" operator="eq" value="${entityId}"/>
                </filter>
            </entity>
        </fetch>`;
    }

    private convertToTimelineNote(note: DynamicsNote): TimelineNote {
        const timelineNote: TimelineNote = {
            noteId: note.annotationid,
            subject: note.subject ?? '',
            noteText: note.notetext ?? '',
            createdOn: new Date(note.createdon ?? Date.now())
        };

        // Add attachment info if available
        if (note.filename) {
            timelineNote.attachments = [{
                id: note.annotationid,
                name: note.filename,
                size: note.filesize ?? 0,
                type: note.mimetype ?? 'application/octet-stream',
                content: note.documentbody ?? '',
                uploadDate: new Date(note.createdon ?? Date.now())
            }];
        }

        return timelineNote;
    }

    // getEntitySetName() removed - now using this.entityInfo.entitySetName directly

    private async executeCreateNote(noteData: NoteData): Promise<{ annotationid: string } | null> {
        const xrmWindow = window as XrmWindow;
        if (!xrmWindow.Xrm?.WebApi) {
            return null;
        }

        try {
            const result = await xrmWindow.Xrm.WebApi.createRecord('annotation', noteData);
            return result;
        } catch {
            return null;
        }
    }

    private async executeFetchXml(fetchXml: string): Promise<DynamicsQueryResult> {
        const xrmWindow = window as XrmWindow;
        if (!xrmWindow.Xrm?.WebApi) {
            throw new Error('Xrm.WebApi not available');
        }

        const encoded = encodeURIComponent(fetchXml);
        return xrmWindow.Xrm.WebApi.retrieveMultipleRecords('annotation', `?fetchXml=${encoded}`);
    }

    public handleError(error: unknown): RepositoryError {
        const timestamp = new Date();
        const errorMessage = error instanceof Error ? error.message : String(error);

        // Error logging removed for production

        if (errorMessage.includes('timeout')) {
            return {
                type: RepositoryErrorType.TIMEOUT,
                message: 'Timeline operation timed out',
                details: error,
                timestamp
            };
        }

        if (errorMessage.includes('permission') || errorMessage.includes('access')) {
            return {
                type: RepositoryErrorType.PERMISSION_ERROR,
                message: 'Permission denied for Timeline operation',
                details: error,
                timestamp
            };
        }

        if (errorMessage.includes('not found')) {
            return {
                type: RepositoryErrorType.NOT_FOUND,
                message: 'Timeline resource not found',
                details: error,
                timestamp
            };
        }

        if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
            return {
                type: RepositoryErrorType.NETWORK_ERROR,
                message: 'Network error during Timeline operation',
                details: error,
                timestamp
            };
        }

        return {
            type: RepositoryErrorType.UNKNOWN,
            message: errorMessage || 'Unknown Timeline error occurred',
            details: error,
            timestamp
        };
    }


}
