import * as React from "react";
import {
    But<PERSON>,
    Text,
    Tooltip
} from "@fluentui/react-components";
import { useStrings } from '../localization/LanguageContext';
import type { AttachedFile, FileAttachmentConfig } from '../types';

const AttachIcon = () => <span>📎</span>;
const DeleteIcon = () => <span>🗑️</span>;
const DocumentIcon = () => <span>📄</span>;
const PdfIcon = () => <span>📕</span>;
const WordIcon = () => <span>📘</span>;

const containerStyle: React.CSSProperties = {
    marginTop: '16px',
    padding: '16px',
    border: '1px dashed #d1d1d1',
    borderRadius: '6px',
    textAlign: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease'
};

const dragActiveStyle: React.CSSProperties = {
    borderColor: '#0078d4',
    backgroundColor: '#f3f9fd'
};

const fileInputStyle: React.CSSProperties = {
    display: 'none'
};

const fileListStyle: React.CSSProperties = {
    marginTop: '16px',
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
};

const fileItemStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '8px',
    border: '1px solid #d1d1d1',
    borderRadius: '6px',
    backgroundColor: '#ffffff'
};

const fileInfoStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    flex: 1
};

const fileNameStyle: React.CSSProperties = {
    fontWeight: '600'
};

const fileSizeStyle: React.CSSProperties = {
    color: '#8a8886',
    fontSize: '12px'
};

const actionsStyle: React.CSSProperties = {
    display: 'flex',
    gap: '4px'
};

const infoTextStyle: React.CSSProperties = {
    marginTop: '8px',
    color: '#8a8886',
    fontSize: '12px'
};

const readOnlyContainerStyle: React.CSSProperties = {
    marginTop: '16px',
    padding: '16px',
    border: '1px solid #d1d1d1',
    borderRadius: '6px',
    backgroundColor: '#f8f8f8'
};

export interface FileAttachmentProps {
    files: AttachedFile[];
    onFilesChange: (files: AttachedFile[]) => void;
    config: FileAttachmentConfig;
    readOnly?: boolean;
}

export const FileAttachment: React.FC<FileAttachmentProps> = ({
    files,
    onFilesChange,
    config,
    readOnly = false
}) => {
    const strings = useStrings();
    const fileInputRef = React.useRef<HTMLInputElement>(null);
    const [dragActive, setDragActive] = React.useState(false);

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return strings.ui.fileAttachment.fileSizeUnits.zeroBytes;
        const k = 1024;
        const sizes = [
            strings.ui.fileAttachment.fileSizeUnits.bytes,
            strings.ui.fileAttachment.fileSizeUnits.kb,
            strings.ui.fileAttachment.fileSizeUnits.mb,
            strings.ui.fileAttachment.fileSizeUnits.gb
        ];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `${parseFloat((bytes / (k ** i)).toFixed(2))} ${sizes[i]}`;
    };

    const getFileIcon = (fileType: string) => {
        const type = fileType.toLowerCase();
        if (type.includes('pdf')) return <PdfIcon />;
        if (type.includes('word') || type.includes('doc')) return <WordIcon />;
        return <DocumentIcon />;
    };

    const validateFile = (file: File): string | null => {
        const maxSizeBytes = config.maxSizeInMB * 1024 * 1024;
        if (file.size > maxSizeBytes) {
            return strings.messages.error.fileSizeExceeded;
        }

        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        if (!fileExtension || !config.allowedTypes.includes(fileExtension)) {
            return strings.messages.error.fileTypeNotAllowed;
        }

        return null;
    };

    const handleFileSelect = (selectedFiles: FileList) => {
        if (selectedFiles.length === 0) {
            return;
        }

        const file = selectedFiles[0];
        const validationError = validateFile(file);

        if (validationError) {
            return;
        }

        const placeholderFile: AttachedFile = {
            id: `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            name: file.name,
            size: file.size,
            type: file.type,
            content: '',
            uploadDate: new Date()
        };

        onFilesChange([placeholderFile]);
        const reader = new FileReader();

        reader.onload = () => {
            const base64Content = reader.result as string;
            const content = base64Content.split(',')[1];
            const updatedFile: AttachedFile = {
                ...placeholderFile,
                content: content
            };
            onFilesChange([updatedFile]);
        };

        reader.onerror = () => {
            onFilesChange([]);
        };

        reader.readAsDataURL(file);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setDragActive(false);

        if (readOnly) return;

        const droppedFiles = e.dataTransfer.files;
        if (droppedFiles.length > 0) {
            handleFileSelect(droppedFiles);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        if (!readOnly) {
            setDragActive(true);
        }
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setDragActive(false);
    };

    const handleBrowseClick = () => {
        if (!readOnly && fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleBrowseClick();
        }
    };

    const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFiles = e.target.files;
        if (selectedFiles && selectedFiles.length > 0) {
            handleFileSelect(selectedFiles);
        }
        e.target.value = '';
    };

    const handleRemoveFile = (fileId: string) => {
        if (!readOnly) {
            const updatedFiles = files.filter(file => file.id !== fileId);
            onFilesChange(updatedFiles);
        }
    };

    const handleDownloadFile = (file: AttachedFile) => {
        const link = document.createElement('a');
        link.href = `data:${file.type};base64,${file.content}`;
        link.download = file.name;
        document.body.appendChild(link);
        link.click();

        setTimeout(() => {
            document.body.removeChild(link);
        }, 100);
    };

    if (!config.enabled) {
        return null;
    }

    return (
        <div>
            {!readOnly && files.length === 0 && (
                <button
                    type="button"
                    style={{
                        ...containerStyle,
                        ...(dragActive ? dragActiveStyle : {}),
                        border: 'none',
                        background: 'transparent',
                        padding: 0,
                        cursor: 'pointer'
                    }}
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onClick={handleBrowseClick}
                    onKeyDown={handleKeyDown}
                    aria-label={strings.ui.fileAttachment.dragDropText}
                >
                    <AttachIcon />
                    <Text>
                        {strings.ui.fileAttachment.dragDropText}{' '}
                        <Text style={{ color: '#0078d4', cursor: 'pointer' }}>
                            {strings.ui.fileAttachment.browseText}
                        </Text>
                    </Text>
                    <div style={infoTextStyle}>
                        <div>{strings.ui.fileAttachment.maxSizeText}: {config.maxSizeInMB}MB</div>
                        <div>{strings.ui.fileAttachment.allowedTypesText}: {config.allowedTypes.join(', ')}</div>
                    </div>
                    <input
                        ref={fileInputRef}
                        type="file"
                        accept={config.allowedTypes.map(type => `.${type}`).join(',')}
                        onChange={handleFileInputChange}
                        style={fileInputStyle}
                    />
                </button>
            )}

            {files.length > 0 && (
                <div style={readOnly ? readOnlyContainerStyle : fileListStyle}>
                    <Text weight="semibold">{strings.ui.fileAttachment.attachedFilesTitle}</Text>
                    {files.map((file) => (
                        <div key={file.id} style={fileItemStyle}>
                            <div style={fileInfoStyle}>
                                {getFileIcon(file.type)}
                                <div>
                                    <div style={fileNameStyle}>{file.name}</div>
                                    <div style={fileSizeStyle}>{formatFileSize(file.size)}</div>
                                </div>
                            </div>
                            <div style={actionsStyle}>
                                <Tooltip content={strings.ui.buttons.downloadFile} relationship="label">
                                    <Button
                                        appearance="subtle"
                                        icon={<DocumentIcon />}
                                        onClick={() => handleDownloadFile(file)}
                                    />
                                </Tooltip>
                                {!readOnly && (
                                    <Tooltip content={strings.ui.buttons.removeFile} relationship="label">
                                        <Button
                                            appearance="subtle"
                                            icon={<DeleteIcon />}
                                            onClick={() => handleRemoveFile(file.id)}
                                        />
                                    </Tooltip>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {files.length === 0 && readOnly && (
                <div style={readOnlyContainerStyle}>
                    <Text>{strings.ui.fileAttachment.noFilesText}</Text>
                </div>
            )}
        </div>
    );
};
