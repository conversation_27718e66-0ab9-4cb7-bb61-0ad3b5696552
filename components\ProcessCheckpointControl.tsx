import * as React from "react";
import {
    FluentProvider,
    Input,
    webLightTheme
} from '@fluentui/react-components';
import { useBpfWorkflow } from '../hooks/UseBpfWorkflow';
import { LanguageProvider, useStrings } from '../localization/LanguageContext';
import type { AttachedFile, BpfStageInfo, ProcessCheckpointProps } from '../types';
import { ToastIntentType } from '../types';
import { CommentModal } from './CommentModal';
import { ToastNotification, useToastMessages } from './ToastNotification';

const containerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    fontFamily: '"Segoe UI", "Segoe UI Web (West European)", "Segoe UI", -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif'
};

const inputContainerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative'
};

const inputStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    minHeight: '32px',
    cursor: 'pointer',
    backgroundColor: '#ffffff',
    border: '1px solid #d1d1d1',
    borderRadius: '4px',
    fontSize: '14px',
    fontFamily: 'inherit'
};

// Core component that uses context
const ProcessCheckpointControlCore: React.FC<ProcessCheckpointProps> = ({
	text,
	getTextValue,
	popupTitle,
	textLabel,
	textPlaceHolder,
    bpfHelper,
    targetStageId,
    language,
    enableFileAttachment = false,
    allowedFileTypes = 'pdf,docx,xlsx',
    maxFileSize = 10
}) => {
    const [isModalOpen, setIsModalOpen] = React.useState(false);
    const [textValue, setTextValue] = React.useState(text ?? "");
    const [isReadOnly, setIsReadOnly] = React.useState(false);
    const [isLoading, setIsLoading] = React.useState(true);
    const [attachedFiles, setAttachedFiles] = React.useState<AttachedFile[]>([]);

    // Toast notifications
    const { messages, dismissToast, showSuccess, showError, showWarning, showInfo } = useToastMessages();

    // Localization
    const strings = useStrings();

    // Use custom hook for BPF workflow
    const { actionHandlers, isProcessing } = useBpfWorkflow({
        bpfHelper: bpfHelper ?? null,
        onClose: () => setIsModalOpen(false),
        currentComment: textValue,
        attachedFiles: attachedFiles,
        onCommentSubmitted: () => {
            // Clear textarea and files after successful submission - do NOT save to bound field
            setTextValue("");
            setAttachedFiles([]);
        },
        onCancel: () => {
            // Clear content and files when Cancel is pressed (only for editable mode)
            setTextValue("");
            setAttachedFiles([]);
        },
        isReadOnly: isReadOnly,
        onShowToast: (intent, title, message) => {
            switch (intent) {
                case ToastIntentType.SUCCESS:
                    showSuccess(title, message);
                    break;
                case ToastIntentType.ERROR:
                    showError(title, message);
                    break;
                case ToastIntentType.WARNING:
                    showWarning(title, message);
                    break;
                case ToastIntentType.INFO:
                    showInfo(title, message);
                    break;
            }
        },
        language: language ?? 'vi'
    });

    // Get button configuration from BPF helper
    const buttonConfig = React.useMemo(() => {
        if (!bpfHelper) {
            return {
                showCancel: true,
                showSubmit: false,
                showApprove: false,
                showReject: false
            };
        }
        return bpfHelper.getButtonConfig(targetStageId);
    }, [bpfHelper, targetStageId]);

    // Initialize stage data on mount and when dependencies change
    React.useEffect(() => {
        const initializeStageData = () => {
            setIsLoading(true);
            try {
                if (!bpfHelper) {
                    setTextValue(text ?? "");
                    setIsReadOnly(false);
                    return;
                }

                // Get both selected and active stage for comparison
                const selectedStageInfo: BpfStageInfo | null = bpfHelper.getSelectedStageInfo();
                const activeStageInfo: BpfStageInfo | null = bpfHelper.getCurrentStageInfo();

                if (targetStageId) {
                    // Case 1: targetStageId is provided (specific stage requested)

                    // Check if targetStageId is the active stage
                    const isTargetStageActive = activeStageInfo &&
                        targetStageId === activeStageInfo.stageId;

                    if (isTargetStageActive) {
                        return bpfHelper.isCurrentStageReadOnly()
                            .then((readOnly) => {
                                setIsReadOnly(readOnly);
                                if (readOnly) {
                                    return bpfHelper.getLatestStageComment();
                                } else {
                                    setTextValue(text ?? "");
                                    return null;
                                }
                            })
                            .then((stageComment) => {
                                if (stageComment !== null) {
                                    setTextValue(stageComment ?? "");
                                }
                                return stageComment;
                            })
                            .catch(() => {
                                setTextValue(text ?? "");
                                setIsReadOnly(false);
                                return null;
                            })
                            .finally(() => {
                                setIsLoading(false);
                            });
                    } else {
                        setIsReadOnly(true);
                        setTextValue("");
                        setIsLoading(false);
                    }
                } else if (selectedStageInfo && activeStageInfo) {
                    // Case 2: No targetStageId - use selected vs active stage logic
                    const isSelectedStageActive = selectedStageInfo.stageId === activeStageInfo.stageId;

                    if (isSelectedStageActive) {
                        return bpfHelper.isCurrentStageReadOnly()
                            .then((readOnly) => {
                                setIsReadOnly(readOnly);
                                if (readOnly) {
                                    return bpfHelper.getLatestStageComment();
                                } else {
                                    setTextValue(text ?? "");
                                    return null;
                                }
                            })
                            .then((stageComment) => {
                                if (stageComment !== null) {
                                    setTextValue(stageComment ?? "");
                                }
                                return stageComment;
                            })
                            .catch(() => {
                                setTextValue(text ?? "");
                                setIsReadOnly(false);
                                return null;
                            })
                            .finally(() => {
                                setIsLoading(false);
                            });
                    } else {
                        // User clicked on previous stage - read-only mode
                        setIsReadOnly(true);
                        // Set placeholder text to indicate comment will load on input click
                        setTextValue("");
                        setIsLoading(false);
                    }
                } else {
                    return bpfHelper.isCurrentStageReadOnly()
                        .then((readOnly) => {
                            setIsReadOnly(readOnly);
                            if (readOnly) {
                                return bpfHelper.getLatestStageComment();
                            } else {
                                setTextValue(text ?? "");
                                return null;
                            }
                        })
                        .then((existingComment) => {
                            if (existingComment !== null) {
                                setTextValue(existingComment ?? "");
                            }
                            return existingComment;
                        })
                        .catch(() => {
                            setTextValue(text ?? "");
                            setIsReadOnly(false);
                            return null;
                        })
                        .finally(() => {
                            setIsLoading(false);
                        });
                }
            } catch {
                setTextValue(text ?? "");
                setIsLoading(false);
            }
        };

        void initializeStageData();
    }, [bpfHelper, text, getTextValue, targetStageId]);

    // Update textValue when text prop changes (for non-read-only stages)
    React.useEffect(() => {
        if (!isReadOnly && !isLoading) {
            setTextValue(text ?? "");
        }
    }, [text, isReadOnly, isLoading]);

    const handleInputClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();

        if (!isModalOpen) {
            // If this is a read-only stage, load the comment when user clicks to view
            if (isReadOnly && bpfHelper) {
                // Load comment using Promise chain
                if (targetStageId) {
                    bpfHelper.getStageComment(targetStageId)
                        .then((stageComment) => {
                            setTextValue(stageComment ?? "");
                            return stageComment;
                        })
                        .catch(() => {
                            setTextValue("");
                            return null;
                        });
                } else {
                    const selectedStageInfo = bpfHelper.getSelectedStageInfo();
                    if (selectedStageInfo) {
                        bpfHelper.getStageComment(selectedStageInfo.stageId)
                            .then((stageComment) => {
                                setTextValue(stageComment ?? "");
                                return stageComment;
                            })
                            .catch(() => {
                                setTextValue("");
                                return null;
                            });
                    }
                }
            }

            setIsModalOpen(true);
        }
    };

    const handleCommentChange = (value: string) => {
        setTextValue(value);
        // Note: Do NOT call getTextValue here - only update local state
        // getTextValue is only for bound field updates, not for timeline notes
    };

    const handleModalOpenChange = (open: boolean) => {
        setIsModalOpen(open);
    };

    if (isLoading) {
        return (
            <FluentProvider theme={webLightTheme} style={{ width: '100%', height: '100%' }}>
                <div style={containerStyle}>
                    <div style={inputContainerStyle}>
                        <Input
                            value={strings.ui.placeholders.loadingStageData}
                            style={inputStyle}
                            placeholder={strings.ui.placeholders.loadingStageData}
                            readOnly
                        />
                    </div>
                </div>
            </FluentProvider>
        );
    }

    return (
        <FluentProvider theme={webLightTheme} style={{ width: '100%', height: '100%' }}>
            <div style={containerStyle}>
                <div style={inputContainerStyle}>
                    <Input
                        value={textValue}
                        style={{
                            ...inputStyle,
                            backgroundColor: isReadOnly ? '#f8f8f8' : '#ffffff',
                            borderColor: isReadOnly ? '#d2d0ce' : '#605e5c',
                            cursor: 'pointer',
                            color: isReadOnly ? '#8a8886' : '#323130'
                        }}
                        onClick={handleInputClick}
                        placeholder={isReadOnly
                            ? (textValue ? '' : strings.ui.placeholders.clickToViewComment)
                            : textPlaceHolder
                        }
                        readOnly
                    />
                </div>
            </div>

            {isModalOpen && (
                <CommentModal
                    open={isModalOpen}
                    onOpenChange={handleModalOpenChange}
                    value={textValue}
                    onChange={handleCommentChange}
                    modalTitle={popupTitle}
                    textLabel={textLabel}
                    textPlaceHolder={textPlaceHolder}
                    bpfActionHandlers={actionHandlers}
                    buttonConfig={buttonConfig}
                    isProcessing={isProcessing}
                    readOnly={isReadOnly}
                    enableFileAttachment={enableFileAttachment}
                    allowedFileTypes={allowedFileTypes ? allowedFileTypes.split(',').map(t => t.trim()) : []}
                    maxFileSize={maxFileSize}
                    attachedFiles={attachedFiles}
                    onFilesChange={setAttachedFiles}
                />
            )}

            <ToastNotification
                messages={messages}
                onDismiss={dismissToast}
            />
        </FluentProvider>
    );
};

// Main component with Language Provider
export const ProcessCheckpointControl: React.FC<ProcessCheckpointProps> = (props) => {
    return (
        <LanguageProvider language={props.language ?? 'vi'}>
            <ProcessCheckpointControlCore {...props} />
        </LanguageProvider>
    );
};

// Export alias for index.ts
export const App = ProcessCheckpointControl;
