/**
 * Repository Factory Functions
 * Simple factory functions for creating repository instances
 */

import type {
    EntityInfo,
    IBpfRepository,
    ITimelineRepository,
    RepositoryOptions
} from '../types';
import { BpfRepository } from './BpfRepository';
import { TimelineRepository } from './TimelineRepository';

export function createBpfRepository(options?: RepositoryOptions): IBpfRepository {
    return new BpfRepository(options);
}

export function createTimelineRepository(entityInfo: EntityInfo, options?: RepositoryOptions): ITimelineRepository {
    return new TimelineRepository(entityInfo, options);
}
