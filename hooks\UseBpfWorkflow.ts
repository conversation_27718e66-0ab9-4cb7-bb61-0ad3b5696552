import { useState } from 'react';
import type { AttachedFile, BpfActionHandlers, BpfActionName, BpfActionResult, IBpfHelper, ToastIntent } from '../types';
import { BpfActionType, ToastIntentType } from '../types';
import { getLocalizedStrings, type SupportedLanguage } from './UseLocalization';

interface UseBpfWorkflowProps {
    bpfHelper: IBpfHelper | null;
    onClose: () => void;
    currentComment?: string;
    attachedFiles?: AttachedFile[];
    onCommentSubmitted?: () => void;
    onCancel?: () => void;
    onShowToast?: (intent: ToastIntent, title: string, message?: string) => void;
    language?: SupportedLanguage;
    isReadOnly?: boolean;
}

/**
 * UI Workflow Orchestration Hook
 * Manages UI state and coordinates between UI interactions and business logic layer (BpfHelper)
 * Note: This hook orchestrates UI flow but delegates actual business operations to BpfHelper
 */
export const useBpfWorkflow = ({ bpfHelper, onClose, currentComment, attachedFiles, onCommentSubmitted, onCancel, onShowToast, language = 'vi', isReadOnly = false }: UseBpfWorkflowProps) => {
    const [isProcessing, setIsProcessing] = useState(false);
    const strings = getLocalizedStrings(language);

    const executeAction = (
        action: () => Promise<boolean>,
        successMsg: string,
        errorMsg: string,
        actionType?: BpfActionName
    ): Promise<BpfActionResult> => {
        console.log('🎯 WORKFLOW DEBUG - executeAction called:', {
            actionType,
            hasComment: !!currentComment?.trim(),
            hasFiles: !!attachedFiles?.length,
            hasBpfHelper: !!bpfHelper
        });

        setIsProcessing(true);

        if (!bpfHelper) {
            console.log('❌ WORKFLOW DEBUG - No BPF helper available');
            setIsProcessing(false);
            return Promise.resolve({ success: true, message: successMsg });
        }

        const notePromise = (currentComment?.trim() && actionType)
            ? bpfHelper.createStageNote(currentComment.trim(), actionType, attachedFiles)
                .then((created) => {
                    console.log('📝 WORKFLOW DEBUG - Note creation result:', created);
                    if (!created) {
                        onShowToast?.(ToastIntentType.WARNING, strings.messages.warning.noteCreationWarning, strings.messages.warning.noteCreationWarning);
                    }
                    return created;
                })
                .catch((error) => {
                    console.log('❌ WORKFLOW DEBUG - Note creation failed:', error);
                    onShowToast?.(ToastIntentType.ERROR, strings.messages.error.noteCreationFailed, strings.messages.error.noteCreationFailed);
                    return false;
                })
            : Promise.resolve(true);

        console.log('📝 WORKFLOW DEBUG - Note creation needed:', !!(currentComment?.trim() && actionType));

        return notePromise
            .then((noteResult) => {
                console.log('📝 WORKFLOW DEBUG - Note promise resolved:', noteResult);
                console.log('🚀 WORKFLOW DEBUG - Executing BPF action:', actionType);
                return action();
            })
            .then((success) => {
                console.log('✅ WORKFLOW DEBUG - BPF action result:', { actionType, success });
                if (success) {
                    onCommentSubmitted?.();
                    onShowToast?.(ToastIntentType.SUCCESS, successMsg);
                } else {
                    onShowToast?.(ToastIntentType.ERROR, strings.messages.error.operationFailed, errorMsg);
                }
                return { success, message: success ? successMsg : errorMsg };
            })
            .catch((error) => {
                console.log('❌ WORKFLOW DEBUG - Action failed:', { actionType, error });
                const errorMessage = error instanceof Error ? error.message : strings.errors.general.unknownError;
                onShowToast?.(ToastIntentType.ERROR, strings.messages.error.operationFailed, errorMessage);
                return { success: false, error: errorMessage };
            })
            .finally(() => {
                console.log('🏁 WORKFLOW DEBUG - Action completed:', actionType);
                setIsProcessing(false);
            });
    };

    const actionHandlers: BpfActionHandlers = {
        onCancel: () => {
            // In read-only mode, close immediately without confirmation
            if (isReadOnly) {
                onCancel?.();
                onClose();
                return;
            }

            // In editable mode, check if there's content to potentially lose
            if (currentComment?.trim()) {
                const shouldDiscard = window.confirm(
                    strings.messages.confirmation.discardChanges
                );
                if (shouldDiscard) {
                    onCancel?.(); // Clear content only if confirmed
                    onClose();
                }
                // If not confirmed, stay in popup
            } else {
                // No content to lose, close immediately
                onClose();
            }
        },
        onSubmit: () => executeAction(
            () => {
                if (!bpfHelper) {
                    throw new Error(strings.messages.error.bpfNotAvailable);
                }
                return bpfHelper.moveToNextStage();
            },
            strings.messages.success.submitted,
            strings.messages.error.submitFailed,
            BpfActionType.SUBMIT
        ),
        onApprove: () => executeAction(
            () => {
                if (!bpfHelper) {
                    throw new Error(strings.messages.error.bpfNotAvailable);
                }
                return bpfHelper.moveToNextStage();
            },
            strings.messages.success.approved,
            strings.messages.error.approveFailed,
            BpfActionType.APPROVE
        ),
        onReject: () => executeAction(
            () => {
                if (!bpfHelper) {
                    throw new Error(strings.messages.error.bpfNotAvailable);
                }
                return bpfHelper.moveToPreviousStage();
            },
            strings.messages.success.rejected,
            strings.messages.error.rejectFailed,
            BpfActionType.REJECT
        )
    };

    return {
        actionHandlers,
        isProcessing
    };
};
