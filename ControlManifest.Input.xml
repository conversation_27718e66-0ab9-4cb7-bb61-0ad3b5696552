<?xml version="1.0" encoding="utf-8" ?>
<manifest>
  <control namespace="PCFControls" constructor="ProcessCheckpoint" version="0.0.2" display-name-key="Process Checkpoint" description-key="Process Checkpoint Control for Business Process Flow approval and navigation" control-type="virtual" >
    <type-group name="strings">
      <type>Multiple</type>
      <type>SingleLine.Text</type>
      <type>SingleLine.TextArea</type>
    </type-group>
    <external-service-usage enabled="false">
    </external-service-usage>
    <property name="sourceControl" display-name-key="Field" description-key="Source field" of-type-group="strings" usage="bound" required="true" />
    <property name="popupTitle" display-name-key="Popup title" description-key="The header title of the popup." of-type="SingleLine.Text" usage="input" required="false" />
    <property name="textLabel" display-name-key="Label" description-key="The label text displayed next to the TextBox." of-type="SingleLine.Text" usage="input" required="false" />
    <property name="textPlaceHolder" display-name-key="Placeholder" description-key="The placeholder text shown inside the TextBox when it is empty." of-type="SingleLine.Text" usage="input" required="false" />
    <property name="language" display-name-key="Language" description-key="Language for the control (vi or en)" of-type="SingleLine.Text" usage="input" required="false" />
    <property name="enableFileAttachment" display-name-key="Enable File Attachment" description-key="Enable or disable file attachment feature" of-type="TwoOptions" usage="input" required="false" />
    <property name="allowedFileTypes" display-name-key="Allowed File Types" description-key="Comma-separated list of allowed file extensions (e.g., pdf,docx,xlsx)" of-type="SingleLine.Text" usage="input" required="false" />
    <property name="maxFileSize" display-name-key="Max File Size (MB)" description-key="Maximum file size in MB (default: 10)" of-type="Whole.None" usage="input" required="false" />
    <resources>
      <code path="index.ts" order="1"/>
      <platform-library name="React" version="16.14.0" />
      <platform-library name="Fluent" version="9.46.2" />
    </resources>
  </control>
</manifest>
