import * as React from "react";
import { withErrorBoundary } from "./components/ErrorBoundary";
import { App } from "./components/ProcessCheckpointControl";
import type { IInputs, IOutputs } from "./generated/ManifestTypes";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./helpers/BpfHelper";
import { getLocalizedStrings } from "./hooks/UseLocalization";

export class ProcessCheckpoint implements ComponentFramework.ReactControl<IInputs, IOutputs> {
    private notifyOutputChanged: () => void;
    private text: string;
    private sourceControl: string;
    private bpfHelper: BpfHelper | null = null;

    public init(
        _context: ComponentFramework.Context<IInputs>,
        notifyOutputChanged: () => void,
        _state: ComponentFramework.Dictionary
    ): void {
        this.notifyOutputChanged = notifyOutputChanged;

        try {
            // Initialize BpfHelper with repository options for better debugging in development
            const repositoryOptions = {
                enableLogging: false, // Set to true for development debugging
                timeout: 5000,
                retryCount: 3
            };
            this.bpfHelper = new BpfHelper(repositoryOptions);
        } catch {
            this.bpfHelper = null;
        }
    }

    public updateView(context: ComponentFramework.Context<IInputs>): React.ReactElement {
        this.text = context.parameters.sourceControl?.raw ?? "";
        this.sourceControl = this.text;
        const title = context.parameters.popupTitle?.raw ?? "";
        const label = context.parameters.textLabel?.raw ?? "";
        const placeholder = context.parameters.textPlaceHolder?.raw ?? "";
        const lang = context.parameters.language?.raw ?? "vi";
        const enableFiles = context.parameters.enableFileAttachment?.raw ?? false;
        const fileTypes = context.parameters.allowedFileTypes?.raw ?? "pdf,docx,xlsx";
        const maxSize = context.parameters.maxFileSize?.raw ?? 10;

        const language = (lang === 'en' || lang === 'vi') ? lang : 'vi';
        const SafeApp = withErrorBoundary(App, undefined, language);
        const strings = getLocalizedStrings(language);

        return React.createElement(
            SafeApp,
            {
                text: this.text,
                getTextValue: this.getTextValue.bind(this),
                popupTitle: title ?? strings.ui.titles.defaultPopup,
                textLabel: label ?? strings.ui.labels.defaultLabel,
                textPlaceHolder: placeholder ?? strings.ui.placeholders.defaultInput,
                bpfHelper: this.bpfHelper,
                language: language,
                enableFileAttachment: enableFiles,
                allowedFileTypes: fileTypes,
                maxFileSize: maxSize
            }
        );
    }

    /**
     * It is called by the framework prior to a control receiving new data.
     * @returns an object based on nomenclature defined in manifest, expecting object[s] for property marked as "bound" or "output"
     */
    public getOutputs(): IOutputs {
        return {
            sourceControl: this.sourceControl
        };
    }

    /**
     * Called when the control is to be removed from the DOM tree. Controls should use this call for cleanup.
     * i.e. cancelling any pending remote calls, removing listeners, etc.
     */
    public destroy(): void {
        // Add code to cleanup control if necessary
    }

    private getTextValue(value: string) {
        this.text = value;
        this.sourceControl = value;
        this.notifyOutputChanged();
    }
}